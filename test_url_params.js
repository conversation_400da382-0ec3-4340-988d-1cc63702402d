// 测试URL参数功能
const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testUrlParameters() {
    console.log('🧪 测试URL参数功能...\n');

    const testUrls = [
        {
            name: '基本URL',
            url: `${BASE_URL}/`,
            description: '测试基本页面访问'
        },
        {
            name: 'PDF文件类型参数',
            url: `${BASE_URL}/?fileType=pdf`,
            description: '测试fileType=pdf参数'
        },
        {
            name: 'Word文件类型参数',
            url: `${BASE_URL}/?fileType=word`,
            description: '测试fileType=word参数'
        },
        {
            name: '关键词参数',
            url: `${BASE_URL}/?keyword=数据治理`,
            description: '测试keyword参数'
        },
        {
            name: '站点参数',
            url: `${BASE_URL}/?site=gov`,
            description: '测试site=gov参数'
        },
        {
            name: '组合参数',
            url: `${BASE_URL}/?fileType=pdf&keyword=人工智能&site=edu`,
            description: '测试多个参数组合'
        },
        {
            name: '编码参数',
            url: `${BASE_URL}/?keyword=${encodeURIComponent('机器学习技术')}&fileType=pdf`,
            description: '测试中文关键词编码'
        }
    ];

    for (const test of testUrls) {
        console.log(`📋 ${test.name}`);
        console.log(`   URL: ${test.url}`);
        console.log(`   说明: ${test.description}`);

        try {
            const response = await axios.get(test.url, {
                timeout: 5000,
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            });

            if (response.status === 200) {
                console.log('   ✅ 页面访问成功');
                
                // 检查页面内容是否包含必要的元素
                const html = response.data;
                const checks = [
                    { name: 'HTML结构', test: html.includes('<!DOCTYPE html>') },
                    { name: '页面标题', test: html.includes('<title>SOSO - 文档搜索下载工具</title>') },
                    { name: '搜索表单', test: html.includes('id="searchForm"') },
                    { name: '文件类型选项', test: html.includes('name="fileType"') },
                    { name: 'JavaScript文件', test: html.includes('js/app.js') },
                    { name: 'CSS样式', test: html.includes('css/style.css') }
                ];

                checks.forEach(check => {
                    if (check.test) {
                        console.log(`   ✅ ${check.name}: 正常`);
                    } else {
                        console.log(`   ❌ ${check.name}: 缺失`);
                    }
                });

            } else {
                console.log(`   ❌ 页面访问失败: HTTP ${response.status}`);
            }
        } catch (error) {
            console.log(`   ❌ 请求失败: ${error.message}`);
        }

        console.log(''); // 空行分隔
    }

    // 测试API端点
    console.log('🔌 测试API端点...\n');

    const apiTests = [
        {
            name: '健康检查',
            url: `${BASE_URL}/health`,
            method: 'GET'
        },
        {
            name: '搜索API',
            url: `${BASE_URL}/api/search`,
            method: 'POST',
            data: {
                keyword: '测试',
                fileType: 'pdf',
                site: 'all',
                page: 1
            }
        },
        {
            name: '文件类型API',
            url: `${BASE_URL}/api/search/file-types`,
            method: 'GET'
        },
        {
            name: '站点API',
            url: `${BASE_URL}/api/search/sites`,
            method: 'GET'
        }
    ];

    for (const test of apiTests) {
        console.log(`📡 ${test.name}`);
        console.log(`   ${test.method} ${test.url}`);

        try {
            let response;
            if (test.method === 'GET') {
                response = await axios.get(test.url, { timeout: 5000 });
            } else {
                response = await axios.post(test.url, test.data, { timeout: 5000 });
            }

            if (response.status === 200) {
                console.log('   ✅ API调用成功');
                if (response.data.success !== undefined) {
                    console.log(`   📊 响应状态: ${response.data.success ? '成功' : '失败'}`);
                }
            } else {
                console.log(`   ❌ API调用失败: HTTP ${response.status}`);
            }
        } catch (error) {
            console.log(`   ❌ API调用失败: ${error.message}`);
        }

        console.log('');
    }

    console.log('🎉 URL参数测试完成！');
    console.log('\n💡 使用建议:');
    console.log('   1. 在浏览器中访问: http://localhost:3000/?fileType=pdf');
    console.log('   2. 检查PDF选项是否自动选中');
    console.log('   3. 尝试其他参数组合进行测试');
    console.log('   4. 确保页面功能正常工作');
}

// 运行测试
if (require.main === module) {
    testUrlParameters().catch(console.error);
}

module.exports = { testUrlParameters };
