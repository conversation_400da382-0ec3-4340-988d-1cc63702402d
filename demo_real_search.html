<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SOSO 真实搜索演示</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4rem 0;
        }
        .demo-card {
            border: none;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-radius: 15px;
            overflow: hidden;
        }
        .demo-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        .demo-link {
            text-decoration: none;
            color: inherit;
        }
        .demo-link:hover {
            color: inherit;
        }
        .feature-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.5rem;
        }
        .api-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            font-size: 0.75rem;
        }
    </style>
</head>
<body>
    <!-- 英雄区域 -->
    <div class="hero-section">
        <div class="container text-center">
            <h1 class="display-3 fw-bold mb-4">🚀 SOSO 真实搜索演示</h1>
            <p class="lead fs-4 mb-4">体验真实API驱动的文档搜索功能</p>
            <p class="fs-5 mb-4">集成arXiv、GitHub、Wikipedia等多个数据源</p>
            <div class="d-flex justify-content-center gap-3">
                <a href="index.html" class="btn btn-light btn-lg">
                    <i class="bi bi-play-fill me-2"></i>开始使用
                </a>
                <a href="test_real_search.html" class="btn btn-outline-light btn-lg">
                    <i class="bi bi-gear me-2"></i>API测试
                </a>
            </div>
        </div>
    </div>

    <div class="container my-5">
        <!-- 搜索演示 -->
        <div class="row mb-5">
            <div class="col-12 text-center mb-4">
                <h2 class="fw-bold">🎯 搜索演示</h2>
                <p class="text-muted">点击下面的卡片体验不同类型的真实搜索</p>
            </div>
            
            <div class="col-md-6 col-lg-3 mb-4">
                <a href="index.html?keyword=机器学习&fileType=pdf" class="demo-link" target="_blank">
                    <div class="card demo-card h-100 position-relative">
                        <span class="badge bg-primary api-badge">arXiv API</span>
                        <div class="card-body text-center p-4">
                            <div class="feature-icon bg-primary bg-opacity-10 text-primary">
                                🤖
                            </div>
                            <h5 class="card-title fw-bold">机器学习论文</h5>
                            <p class="card-text text-muted">搜索最新的机器学习学术论文和研究报告</p>
                            <div class="mt-3">
                                <span class="badge bg-primary me-1">PDF</span>
                                <span class="badge bg-outline-secondary">学术</span>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
            
            <div class="col-md-6 col-lg-3 mb-4">
                <a href="index.html?keyword=深度学习&fileType=all&site=edu" class="demo-link" target="_blank">
                    <div class="card demo-card h-100 position-relative">
                        <span class="badge bg-success api-badge">GitHub API</span>
                        <div class="card-body text-center p-4">
                            <div class="feature-icon bg-success bg-opacity-10 text-success">
                                💻
                            </div>
                            <h5 class="card-title fw-bold">深度学习项目</h5>
                            <p class="card-text text-muted">查找深度学习相关的开源项目和代码库</p>
                            <div class="mt-3">
                                <span class="badge bg-success me-1">代码</span>
                                <span class="badge bg-outline-secondary">开源</span>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
            
            <div class="col-md-6 col-lg-3 mb-4">
                <a href="index.html?keyword=人工智能&fileType=word" class="demo-link" target="_blank">
                    <div class="card demo-card h-100 position-relative">
                        <span class="badge bg-info api-badge">Wikipedia</span>
                        <div class="card-body text-center p-4">
                            <div class="feature-icon bg-info bg-opacity-10 text-info">
                                📚
                            </div>
                            <h5 class="card-title fw-bold">人工智能百科</h5>
                            <p class="card-text text-muted">获取人工智能的权威百科知识和参考资料</p>
                            <div class="mt-3">
                                <span class="badge bg-info me-1">百科</span>
                                <span class="badge bg-outline-secondary">权威</span>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
            
            <div class="col-md-6 col-lg-3 mb-4">
                <a href="index.html?keyword=区块链&fileType=ppt&site=gov" class="demo-link" target="_blank">
                    <div class="card demo-card h-100 position-relative">
                        <span class="badge bg-warning api-badge">News API</span>
                        <div class="card-body text-center p-4">
                            <div class="feature-icon bg-warning bg-opacity-10 text-warning">
                                📰
                            </div>
                            <h5 class="card-title fw-bold">区块链资讯</h5>
                            <p class="card-text text-muted">查找区块链相关的最新新闻和政策文件</p>
                            <div class="mt-3">
                                <span class="badge bg-warning me-1">资讯</span>
                                <span class="badge bg-outline-secondary">实时</span>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
        </div>

        <!-- 功能特点 -->
        <div class="row mb-5">
            <div class="col-12 text-center mb-4">
                <h2 class="fw-bold">✨ 功能特点</h2>
            </div>
            
            <div class="col-md-6 col-lg-3 mb-4">
                <div class="text-center">
                    <div class="feature-icon bg-primary bg-opacity-10 text-primary">
                        <i class="bi bi-lightning-charge-fill"></i>
                    </div>
                    <h5 class="fw-bold">真实API数据</h5>
                    <p class="text-muted">集成多个真实API，提供最新、最准确的搜索结果</p>
                </div>
            </div>
            
            <div class="col-md-6 col-lg-3 mb-4">
                <div class="text-center">
                    <div class="feature-icon bg-success bg-opacity-10 text-success">
                        <i class="bi bi-globe"></i>
                    </div>
                    <h5 class="fw-bold">多数据源整合</h5>
                    <p class="text-muted">同时搜索学术、开源、百科、新闻等多个领域</p>
                </div>
            </div>
            
            <div class="col-md-6 col-lg-3 mb-4">
                <div class="text-center">
                    <div class="feature-icon bg-info bg-opacity-10 text-info">
                        <i class="bi bi-download"></i>
                    </div>
                    <h5 class="fw-bold">直接下载</h5>
                    <p class="text-muted">点击即可下载真实的文档和资源文件</p>
                </div>
            </div>
            
            <div class="col-md-6 col-lg-3 mb-4">
                <div class="text-center">
                    <div class="feature-icon bg-warning bg-opacity-10 text-warning">
                        <i class="bi bi-phone"></i>
                    </div>
                    <h5 class="fw-bold">响应式设计</h5>
                    <p class="text-muted">完美适配桌面、平板、手机等各种设备</p>
                </div>
            </div>
        </div>

        <!-- 使用说明 -->
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="card border-0 bg-light">
                    <div class="card-body p-4">
                        <h3 class="text-center mb-4">📖 使用说明</h3>
                        <div class="row">
                            <div class="col-md-6">
                                <h5>🔍 搜索步骤</h5>
                                <ol class="list-unstyled">
                                    <li class="mb-2">1️⃣ 选择文件类型（默认PDF）</li>
                                    <li class="mb-2">2️⃣ 输入搜索关键词</li>
                                    <li class="mb-2">3️⃣ 选择搜索站点（可选）</li>
                                    <li class="mb-2">4️⃣ 点击搜索按钮</li>
                                </ol>
                            </div>
                            <div class="col-md-6">
                                <h5>📥 下载方式</h5>
                                <ul class="list-unstyled">
                                    <li class="mb-2">🔗 单击下载：直接打开文档链接</li>
                                    <li class="mb-2">☑️ 批量选择：勾选多个文档</li>
                                    <li class="mb-2">📦 批量下载：一键下载所有选中文档</li>
                                    <li class="mb-2">🔄 实时状态：显示下载进度和状态</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container text-center">
            <p class="mb-2">
                <i class="bi bi-heart-fill text-danger me-1"></i>
                SOSO 文档搜索工具 - 真实API驱动的搜索体验
            </p>
            <p class="small text-muted mb-0">
                集成 arXiv、GitHub、Wikipedia 等多个数据源 | 纯前端实现，无需后端服务器
            </p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
