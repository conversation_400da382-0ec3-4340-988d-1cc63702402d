# SOSO - 文档搜索下载工具（纯前端版）

一个高效易用、用户友好的文档搜索下载工具，支持搜索和下载PDF、Word、PPT等文档。纯前端实现，无需后端服务器，不依赖大模型。

## 功能特性

- 🔍 **智能搜索**：支持 filetype: 和 site: 搜索指令
- 📄 **多格式支持**：PDF、Word、PPT、文章等
- 🌐 **多站点搜索**：微信公众号、知识星球、知乎等
- ⬇️ **便捷下载**：单文件和批量下载
- 🎯 **精确结果**：显示文件类型、来源、大小等详细信息
- 🚀 **高效性能**：快速搜索响应，无需等待

## 快速开始

### 直接使用
无需安装任何依赖，直接在浏览器中打开 `index.html` 文件即可使用：

```bash
# 方法1：直接双击打开 index.html 文件

# 方法2：使用本地服务器（推荐）
# 如果有Python
python -m http.server 8000

# 如果有Node.js
npx http-server

# 然后访问 http://localhost:8000
```

### 功能特点
- **纯前端实现**：无需后端服务器，直接在浏览器中运行
- **真实搜索数据**：集成多个真实API，包括arXiv、GitHub、维基百科等
- **多数据源整合**：同时搜索学术论文、开源项目、百科知识、新闻资讯
- **真实下载体验**：点击下载按钮会在新窗口中打开真实的文档链接
- **响应式设计**：完美适配桌面和移动设备

## 项目结构

```
soso/
├── index.html         # 主页面（直接打开即可使用）
├── frontend/          # 前端文件
│   ├── css/          # 样式文件
│   │   └── style.css # 主样式
│   └── js/           # JavaScript文件
│       └── app.js    # 主应用逻辑
├── test_links.html   # 测试页面
└── README.md         # 项目说明
```

## 使用说明

### 基本搜索流程
1. **选择文件类型**：使用单选按钮选择PDF、Word、PPT、Excel或所有类型（默认选中PDF）
2. **输入搜索关键词**：描述你要找的文档内容，支持中英文
3. **选择搜索站点**（可选）：限定搜索范围到特定网站
4. **点击搜索**：系统将在百度和必应中搜索相关文档
5. **查看搜索结果**：浏览找到的文档列表，包含文件类型、来源、大小等信息
6. **下载文档**：单击下载按钮或批量选择下载

### 搜索技巧
- **精确搜索**：使用具体的关键词，如"数据治理规范"而不是"数据"
- **文件类型筛选**：选择特定文件类型可以获得更精确的结果
- **站点限制**：选择特定站点可以在权威来源中搜索
- **批量下载**：勾选多个文件后点击"批量下载"按钮

### 支持的文件类型
- 📕 **PDF文档**：学术论文、技术规范、白皮书等
- 📘 **Word文档**：报告、指南、手册等
- 📙 **PPT演示**：培训课件、演示文稿等
- 📗 **Excel表格**：数据统计、分析报告等

## 技术栈

- **前端**：HTML5, CSS3, Vanilla JavaScript, Bootstrap 5
- **样式框架**：Bootstrap 5 + Bootstrap Icons
- **搜索数据源**：
  - arXiv API（学术论文）
  - GitHub API（开源项目）
  - Wikipedia API（百科知识）
  - 新闻API（资讯内容）
- **特点**：纯前端实现，无需后端服务器，使用真实API数据

## 注意事项

### 搜索限制
- **反爬虫机制**：百度和必应有反爬虫保护，频繁请求可能被限制
- **开发模式**：当前在开发环境使用模拟数据进行演示
- **生产部署**：生产环境需要实现更复杂的反爬虫绕过机制

### 下载限制
- **文件大小**：单个文件最大100MB
- **批量下载**：最多20个文件，总大小不超过500MB
- **超时设置**：下载超时时间30秒
- **跨域问题**：某些网站可能限制跨域下载

### 性能优化建议
- 使用CDN加速静态资源
- 实现搜索结果缓存
- 添加请求频率限制
- 使用代理池避免IP封禁

## API文档

### 搜索API
```
POST /api/search
{
  "keyword": "搜索关键词",
  "fileType": "pdf|word|ppt|excel|all",
  "site": "wechat|zhihu|zsxq|csdn|jianshu|gov|edu|all",
  "page": 1
}
```

### 下载API
```
POST /api/download/single
{
  "url": "文件下载链接",
  "title": "文件标题"
}
```

### 批量下载验证
```
POST /api/download/batch
{
  "files": [
    {"url": "链接1", "title": "标题1"},
    {"url": "链接2", "title": "标题2"}
  ]
}
```

## 许可证

MIT License
