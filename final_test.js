// 最终功能验证脚本
const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function runFinalTest() {
    console.log('🎯 SOSO 文档搜索工具 - 最终功能验证');
    console.log('=' .repeat(60));
    
    let passedTests = 0;
    let totalTests = 0;

    // 测试1: 服务器健康检查
    console.log('\n📊 测试1: 服务器健康检查');
    totalTests++;
    try {
        const response = await axios.get(`${BASE_URL}/health`);
        if (response.data.status === 'OK') {
            console.log('✅ 服务器运行正常');
            passedTests++;
        } else {
            console.log('❌ 服务器状态异常');
        }
    } catch (error) {
        console.log('❌ 服务器连接失败:', error.message);
    }

    // 测试2: 页面访问测试
    console.log('\n📄 测试2: 页面访问测试');
    const pageTests = [
        { name: '基本页面', url: '/' },
        { name: 'PDF参数页面', url: '/?fileType=pdf' },
        { name: '关键词参数页面', url: '/?keyword=测试' },
        { name: '组合参数页面', url: '/?fileType=pdf&keyword=数据治理&site=gov' }
    ];

    for (const test of pageTests) {
        totalTests++;
        try {
            const response = await axios.get(`${BASE_URL}${test.url}`);
            if (response.status === 200 && response.data.includes('SOSO')) {
                console.log(`✅ ${test.name}: 访问成功`);
                passedTests++;
            } else {
                console.log(`❌ ${test.name}: 页面内容异常`);
            }
        } catch (error) {
            console.log(`❌ ${test.name}: 访问失败`);
        }
    }

    // 测试3: API功能测试
    console.log('\n🔌 测试3: API功能测试');
    
    // 搜索API测试
    totalTests++;
    try {
        const searchResponse = await axios.post(`${BASE_URL}/api/search`, {
            keyword: '人工智能',
            fileType: 'pdf',
            site: 'all',
            page: 1
        });
        
        if (searchResponse.data.success && searchResponse.data.data.results.length > 0) {
            console.log('✅ 搜索API: 正常工作');
            console.log(`   找到 ${searchResponse.data.data.results.length} 个结果`);
            passedTests++;
        } else {
            console.log('❌ 搜索API: 返回结果异常');
        }
    } catch (error) {
        console.log('❌ 搜索API: 调用失败');
    }

    // 文件类型API测试
    totalTests++;
    try {
        const fileTypesResponse = await axios.get(`${BASE_URL}/api/search/file-types`);
        if (fileTypesResponse.data.success && fileTypesResponse.data.fileTypes.length > 0) {
            console.log('✅ 文件类型API: 正常工作');
            passedTests++;
        } else {
            console.log('❌ 文件类型API: 返回数据异常');
        }
    } catch (error) {
        console.log('❌ 文件类型API: 调用失败');
    }

    // 站点API测试
    totalTests++;
    try {
        const sitesResponse = await axios.get(`${BASE_URL}/api/search/sites`);
        if (sitesResponse.data.success && sitesResponse.data.sites.length > 0) {
            console.log('✅ 站点API: 正常工作');
            passedTests++;
        } else {
            console.log('❌ 站点API: 返回数据异常');
        }
    } catch (error) {
        console.log('❌ 站点API: 调用失败');
    }

    // 下载检查API测试
    totalTests++;
    try {
        const checkResponse = await axios.post(`${BASE_URL}/api/download/check`, {
            url: 'https://example.com/test.pdf'
        });
        if (checkResponse.data.success) {
            console.log('✅ 下载检查API: 正常工作');
            passedTests++;
        } else {
            console.log('❌ 下载检查API: 返回数据异常');
        }
    } catch (error) {
        console.log('❌ 下载检查API: 调用失败');
    }

    // 测试4: 特定功能验证
    console.log('\n🎯 测试4: 特定功能验证');
    
    // 验证文件类型默认为PDF
    totalTests++;
    try {
        const response = await axios.get(`${BASE_URL}/?fileType=pdf`);
        if (response.data.includes('value="pdf" checked')) {
            console.log('✅ PDF默认选中: 功能正常');
            passedTests++;
        } else {
            console.log('❌ PDF默认选中: 功能异常');
        }
    } catch (error) {
        console.log('❌ PDF默认选中: 验证失败');
    }

    // 验证搜索引擎配置
    totalTests++;
    const baiduEnabled = process.env.BAIDU_SEARCH_ENABLED !== 'false';
    const bingEnabled = process.env.BING_SEARCH_ENABLED !== 'false';
    
    if (baiduEnabled || bingEnabled) {
        console.log('✅ 搜索引擎配置: 正常');
        console.log(`   百度搜索: ${baiduEnabled ? '启用' : '禁用'}`);
        console.log(`   必应搜索: ${bingEnabled ? '启用' : '禁用'}`);
        passedTests++;
    } else {
        console.log('❌ 搜索引擎配置: 所有搜索引擎都被禁用');
    }

    // 测试结果汇总
    console.log('\n📊 测试结果汇总');
    console.log('=' .repeat(60));
    console.log(`总测试数: ${totalTests}`);
    console.log(`通过测试: ${passedTests}`);
    console.log(`失败测试: ${totalTests - passedTests}`);
    console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

    if (passedTests === totalTests) {
        console.log('\n🎉 所有测试通过！SOSO 文档搜索工具功能正常！');
        console.log('\n✨ 主要功能确认:');
        console.log('   ✅ 文件类型使用radio按钮，默认选中PDF');
        console.log('   ✅ 支持百度和必应搜索引擎');
        console.log('   ✅ URL参数处理正常');
        console.log('   ✅ 搜索和下载功能正常');
        console.log('   ✅ 用户界面友好易用');
        
        console.log('\n🚀 使用指南:');
        console.log('   1. 访问: http://localhost:3000');
        console.log('   2. 选择文件类型（默认PDF）');
        console.log('   3. 输入搜索关键词');
        console.log('   4. 选择搜索站点（可选）');
        console.log('   5. 点击搜索并下载文件');
        
        console.log('\n🔗 测试链接:');
        console.log('   基本页面: http://localhost:3000/');
        console.log('   PDF搜索: http://localhost:3000/?fileType=pdf');
        console.log('   组合搜索: http://localhost:3000/?fileType=pdf&keyword=数据治理');
        
    } else {
        console.log('\n⚠️  部分测试失败，请检查相关功能');
    }
}

// 运行最终测试
if (require.main === module) {
    runFinalTest().catch(console.error);
}

module.exports = { runFinalTest };
