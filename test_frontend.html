<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SOSO 纯前端版本测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-link {
            display: block;
            margin: 10px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            text-decoration: none;
            color: #333;
            transition: all 0.2s;
        }
        .test-link:hover {
            background-color: #f8f9fa;
            color: #0d6efd;
            border-color: #0d6efd;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .test-description {
            font-size: 0.9em;
            color: #666;
            margin-top: 8px;
        }
        .feature-card {
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="text-center mb-5">
            <h1 class="display-4">🎯 SOSO 纯前端版本</h1>
            <p class="lead">高效易用的文档搜索下载工具 - 无需后端服务器</p>
        </div>

        <!-- 功能特点 -->
        <div class="row mb-5">
            <div class="col-md-3 mb-3">
                <div class="card feature-card h-100 text-center p-3">
                    <div class="card-body">
                        <i class="bi bi-lightning-charge-fill text-primary" style="font-size: 2rem;"></i>
                        <h5 class="card-title mt-2">纯前端</h5>
                        <p class="card-text small">无需后端服务器，直接在浏览器中运行</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card feature-card h-100 text-center p-3">
                    <div class="card-body">
                        <i class="bi bi-file-earmark-pdf-fill text-danger" style="font-size: 2rem;"></i>
                        <h5 class="card-title mt-2">多格式支持</h5>
                        <p class="card-text small">支持PDF、Word、PPT、Excel等文档格式</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card feature-card h-100 text-center p-3">
                    <div class="card-body">
                        <i class="bi bi-search text-success" style="font-size: 2rem;"></i>
                        <h5 class="card-title mt-2">智能搜索</h5>
                        <p class="card-text small">支持文件类型和站点筛选</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card feature-card h-100 text-center p-3">
                    <div class="card-body">
                        <i class="bi bi-download text-warning" style="font-size: 2rem;"></i>
                        <h5 class="card-title mt-2">便捷下载</h5>
                        <p class="card-text small">单个和批量下载功能</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 测试链接 -->
        <div class="row">
            <div class="col-md-6">
                <h3>🧪 功能测试</h3>
                
                <a href="index.html" class="test-link" target="_blank">
                    <strong>🏠 基本功能测试</strong>
                    <div class="test-description">测试基本搜索功能，默认选中PDF文件类型</div>
                </a>
                
                <a href="index.html?fileType=pdf&keyword=数据处理" class="test-link" target="_blank">
                    <strong>📕 PDF搜索测试</strong>
                    <div class="test-description">测试PDF文档搜索，关键词"数据处理"</div>
                </a>
                
                <a href="index.html?fileType=word&keyword=项目管理" class="test-link" target="_blank">
                    <strong>📘 Word搜索测试</strong>
                    <div class="test-description">测试Word文档搜索，关键词"项目管理"</div>
                </a>
                
                <a href="index.html?fileType=ppt&keyword=人工智能" class="test-link" target="_blank">
                    <strong>📙 PPT搜索测试</strong>
                    <div class="test-description">测试PPT文档搜索，关键词"人工智能"</div>
                </a>
            </div>
            
            <div class="col-md-6">
                <h3>🎯 高级测试</h3>
                
                <a href="index.html?fileType=pdf&keyword=数字化转型&site=gov" class="test-link" target="_blank">
                    <strong>🏛️ 政府网站搜索</strong>
                    <div class="test-description">在政府网站中搜索"数字化转型"PDF文档</div>
                </a>
                
                <a href="index.html?fileType=all&keyword=机器学习&site=edu" class="test-link" target="_blank">
                    <strong>🎓 教育网站搜索</strong>
                    <div class="test-description">在教育网站中搜索"机器学习"相关文档</div>
                </a>
                
                <a href="index.html?fileType=excel&keyword=数据分析&site=csdn" class="test-link" target="_blank">
                    <strong>💻 CSDN搜索</strong>
                    <div class="test-description">在CSDN中搜索"数据分析"Excel文档</div>
                </a>
                
                <a href="index.html?fileType=word&keyword=技术文档&site=zhihu" class="test-link" target="_blank">
                    <strong>🤔 知乎搜索</strong>
                    <div class="test-description">在知乎中搜索"技术文档"Word文档</div>
                </a>
            </div>
        </div>
        
        <!-- 使用说明 -->
        <div class="alert alert-info mt-5">
            <h5>📋 使用说明</h5>
            <ol class="mb-0">
                <li><strong>选择文件类型</strong>：使用单选按钮选择要搜索的文档类型（默认PDF）</li>
                <li><strong>输入关键词</strong>：在搜索框中输入要查找的文档关键词</li>
                <li><strong>选择站点</strong>：可选择特定网站进行搜索</li>
                <li><strong>查看结果</strong>：搜索结果会显示文档标题、来源、大小等信息</li>
                <li><strong>下载文档</strong>：点击下载按钮会在新窗口中打开文档链接</li>
                <li><strong>批量下载</strong>：勾选多个文档后可进行批量下载</li>
            </ol>
        </div>
        
        <!-- 技术特点 -->
        <div class="alert alert-success mt-4">
            <h5>✨ 技术特点</h5>
            <ul class="mb-0">
                <li><strong>纯前端实现</strong>：无需安装Node.js或其他后端服务</li>
                <li><strong>模拟数据</strong>：使用高质量的模拟搜索结果进行演示</li>
                <li><strong>URL参数支持</strong>：支持通过URL参数设置默认搜索条件</li>
                <li><strong>响应式设计</strong>：完美适配桌面和移动设备</li>
                <li><strong>现代化界面</strong>：使用Bootstrap 5构建美观的用户界面</li>
            </ul>
        </div>
        
        <div class="text-center mt-5">
            <a href="index.html" class="btn btn-primary btn-lg">
                🚀 开始使用 SOSO
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
