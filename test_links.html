<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SOSO URL参数测试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-link {
            display: block;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            text-decoration: none;
            color: #333;
            transition: background-color 0.2s;
        }
        .test-link:hover {
            background-color: #f8f9fa;
            color: #0d6efd;
        }
        .test-description {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1 class="mb-4">🧪 SOSO URL参数测试</h1>
        
        <div class="alert alert-info">
            <h5>测试说明</h5>
            <p>点击下面的链接测试不同的URL参数组合，验证页面是否正确处理参数并设置相应的默认值。</p>
        </div>

        <div class="row">
            <div class="col-md-6">
                <h3>📄 文件类型测试</h3>
                
                <a href="http://localhost:3000/?fileType=pdf" class="test-link" target="_blank">
                    <strong>📕 PDF文档测试</strong>
                    <div class="test-description">应该默认选中PDF文件类型</div>
                </a>
                
                <a href="http://localhost:3000/?fileType=word" class="test-link" target="_blank">
                    <strong>📘 Word文档测试</strong>
                    <div class="test-description">应该默认选中Word文件类型</div>
                </a>
                
                <a href="http://localhost:3000/?fileType=ppt" class="test-link" target="_blank">
                    <strong>📙 PPT演示测试</strong>
                    <div class="test-description">应该默认选中PPT文件类型</div>
                </a>
                
                <a href="http://localhost:3000/?fileType=excel" class="test-link" target="_blank">
                    <strong>📗 Excel表格测试</strong>
                    <div class="test-description">应该默认选中Excel文件类型</div>
                </a>
            </div>
            
            <div class="col-md-6">
                <h3>🔍 关键词测试</h3>
                
                <a href="http://localhost:3000/?keyword=数据治理" class="test-link" target="_blank">
                    <strong>数据治理搜索</strong>
                    <div class="test-description">搜索框应该预填"数据治理"</div>
                </a>
                
                <a href="http://localhost:3000/?keyword=人工智能&fileType=pdf" class="test-link" target="_blank">
                    <strong>人工智能PDF搜索</strong>
                    <div class="test-description">预填关键词并选中PDF类型</div>
                </a>
                
                <a href="http://localhost:3000/?keyword=机器学习&site=edu" class="test-link" target="_blank">
                    <strong>机器学习教育网站搜索</strong>
                    <div class="test-description">预填关键词并选择教育网站</div>
                </a>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <h3>🌐 站点测试</h3>
                
                <div class="row">
                    <div class="col-md-4">
                        <a href="http://localhost:3000/?site=wechat" class="test-link" target="_blank">
                            <strong>💬 微信公众号</strong>
                            <div class="test-description">应该选择微信公众号站点</div>
                        </a>
                    </div>
                    
                    <div class="col-md-4">
                        <a href="http://localhost:3000/?site=zhihu" class="test-link" target="_blank">
                            <strong>🤔 知乎</strong>
                            <div class="test-description">应该选择知乎站点</div>
                        </a>
                    </div>
                    
                    <div class="col-md-4">
                        <a href="http://localhost:3000/?site=gov" class="test-link" target="_blank">
                            <strong>🏛️ 政府网站</strong>
                            <div class="test-description">应该选择政府网站</div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <h3>🎯 组合测试</h3>
                
                <a href="http://localhost:3000/?fileType=pdf&keyword=数字化转型&site=gov" class="test-link" target="_blank">
                    <strong>完整参数组合测试</strong>
                    <div class="test-description">PDF + 数字化转型 + 政府网站，应该自动执行搜索</div>
                </a>
                
                <a href="http://localhost:3000/?fileType=word&keyword=项目管理指南&site=edu" class="test-link" target="_blank">
                    <strong>Word文档教育搜索</strong>
                    <div class="test-description">Word + 项目管理指南 + 教育网站</div>
                </a>
                
                <a href="http://localhost:3000/?fileType=ppt&keyword=人工智能技术&site=csdn" class="test-link" target="_blank">
                    <strong>PPT技术搜索</strong>
                    <div class="test-description">PPT + 人工智能技术 + CSDN</div>
                </a>
            </div>
        </div>
        
        <div class="alert alert-success mt-4">
            <h5>✅ 验证要点</h5>
            <ul class="mb-0">
                <li>文件类型单选按钮是否正确选中</li>
                <li>搜索关键词是否正确填入搜索框</li>
                <li>站点下拉框是否选择正确的选项</li>
                <li>有关键词的链接是否自动执行搜索</li>
                <li>URL参数是否在搜索后正确更新</li>
            </ul>
        </div>
        
        <div class="text-center mt-4">
            <a href="http://localhost:3000/" class="btn btn-primary">
                🏠 返回主页
            </a>
        </div>
    </div>
</body>
</html>
