<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SOSO 真实搜索测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .search-demo {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 0;
        }
        .feature-card {
            border: none;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            height: 100%;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .test-button {
            margin: 0.5rem;
            min-width: 200px;
        }
        .api-status {
            padding: 1rem;
            border-radius: 8px;
            margin: 0.5rem 0;
        }
        .status-success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .status-error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .status-warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
    </style>
</head>
<body>
    <!-- 头部演示区 -->
    <div class="search-demo">
        <div class="container text-center">
            <h1 class="display-4 mb-3">🔍 SOSO 真实搜索测试</h1>
            <p class="lead mb-4">测试真实API搜索功能，包括arXiv、GitHub、维基百科等数据源</p>
            <a href="index.html" class="btn btn-light btn-lg">
                <i class="bi bi-arrow-left me-2"></i>返回主应用
            </a>
        </div>
    </div>

    <div class="container mt-5">
        <!-- API状态检查 -->
        <div class="row mb-5">
            <div class="col-12">
                <h3>📡 API状态检查</h3>
                <p class="text-muted">检查各个搜索API的可用性</p>
                
                <div id="apiStatus">
                    <div class="api-status status-warning">
                        <strong>正在检查API状态...</strong>
                    </div>
                </div>
                
                <button class="btn btn-primary" onclick="checkAPIs()">
                    <i class="bi bi-arrow-clockwise me-1"></i>重新检查
                </button>
            </div>
        </div>

        <!-- 搜索功能特点 -->
        <div class="row mb-5">
            <div class="col-md-3 mb-3">
                <div class="card feature-card text-center p-3">
                    <div class="card-body">
                        <i class="bi bi-journal-text text-primary" style="font-size: 2.5rem;"></i>
                        <h5 class="card-title mt-3">arXiv学术搜索</h5>
                        <p class="card-text">搜索最新的学术论文和研究报告</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card feature-card text-center p-3">
                    <div class="card-body">
                        <i class="bi bi-github text-dark" style="font-size: 2.5rem;"></i>
                        <h5 class="card-title mt-3">GitHub项目搜索</h5>
                        <p class="card-text">查找相关的开源项目和代码库</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card feature-card text-center p-3">
                    <div class="card-body">
                        <i class="bi bi-wikipedia text-info" style="font-size: 2.5rem;"></i>
                        <h5 class="card-title mt-3">维基百科搜索</h5>
                        <p class="card-text">获取权威的百科知识和参考资料</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card feature-card text-center p-3">
                    <div class="card-body">
                        <i class="bi bi-newspaper text-success" style="font-size: 2.5rem;"></i>
                        <h5 class="card-title mt-3">新闻资讯搜索</h5>
                        <p class="card-text">查找最新的新闻报道和资讯</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 测试按钮 -->
        <div class="row mb-5">
            <div class="col-12 text-center">
                <h3>🧪 快速测试</h3>
                <p class="text-muted">点击下面的按钮测试不同关键词的搜索效果</p>
                
                <div class="d-flex flex-wrap justify-content-center">
                    <button class="btn btn-outline-primary test-button" onclick="testSearch('人工智能', 'pdf')">
                        🤖 人工智能 (PDF)
                    </button>
                    <button class="btn btn-outline-success test-button" onclick="testSearch('机器学习', 'all')">
                        🧠 机器学习 (全部)
                    </button>
                    <button class="btn btn-outline-info test-button" onclick="testSearch('数据处理', 'pdf')">
                        📊 数据处理 (PDF)
                    </button>
                    <button class="btn btn-outline-warning test-button" onclick="testSearch('区块链', 'word')">
                        ⛓️ 区块链 (Word)
                    </button>
                    <button class="btn btn-outline-danger test-button" onclick="testSearch('深度学习', 'ppt')">
                        🔬 深度学习 (PPT)
                    </button>
                    <button class="btn btn-outline-secondary test-button" onclick="testSearch('云计算', 'all')">
                        ☁️ 云计算 (全部)
                    </button>
                </div>
            </div>
        </div>

        <!-- 搜索结果展示 -->
        <div class="row" id="searchResults" style="display: none;">
            <div class="col-12">
                <h3>🔍 搜索结果</h3>
                <div id="resultsContainer"></div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // API检查函数
        async function checkAPIs() {
            const statusContainer = document.getElementById('apiStatus');
            statusContainer.innerHTML = '<div class="api-status status-warning"><strong>正在检查API状态...</strong></div>';
            
            const apis = [
                {
                    name: 'arXiv API',
                    url: 'https://export.arxiv.org/api/query?search_query=all:test&start=0&max_results=1',
                    description: '学术论文搜索'
                },
                {
                    name: 'GitHub API',
                    url: 'https://api.github.com/search/repositories?q=test&per_page=1',
                    description: '开源项目搜索'
                },
                {
                    name: 'Wikipedia API',
                    url: 'https://zh.wikipedia.org/api/rest_v1/page/summary/测试',
                    description: '维基百科搜索'
                }
            ];
            
            let statusHTML = '';
            
            for (const api of apis) {
                try {
                    const response = await fetch(api.url);
                    if (response.ok) {
                        statusHTML += `
                            <div class="api-status status-success">
                                <strong>✅ ${api.name}</strong> - ${api.description} (状态: 正常)
                            </div>
                        `;
                    } else {
                        statusHTML += `
                            <div class="api-status status-error">
                                <strong>❌ ${api.name}</strong> - ${api.description} (状态: HTTP ${response.status})
                            </div>
                        `;
                    }
                } catch (error) {
                    statusHTML += `
                        <div class="api-status status-error">
                            <strong>❌ ${api.name}</strong> - ${api.description} (错误: ${error.message})
                        </div>
                    `;
                }
            }
            
            statusContainer.innerHTML = statusHTML;
        }
        
        // 测试搜索函数
        function testSearch(keyword, fileType) {
            const url = `index.html?keyword=${encodeURIComponent(keyword)}&fileType=${fileType}`;
            window.open(url, '_blank');
        }
        
        // 页面加载时自动检查API
        document.addEventListener('DOMContentLoaded', function() {
            checkAPIs();
        });
    </script>
</body>
</html>
