// SOSO 文档搜索工具主应用
class SosoApp {
    constructor() {
        this.currentPage = 1;
        this.currentQuery = '';
        this.currentFileType = 'all';
        this.currentSite = 'all';
        this.selectedFiles = new Set();
        this.searchResults = [];
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadInitialData();
    }

    bindEvents() {
        // 搜索表单提交
        document.getElementById('searchForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.performSearch();
        });

        // 全选按钮
        document.getElementById('selectAllBtn').addEventListener('click', () => {
            this.toggleSelectAll();
        });

        // 批量下载按钮
        document.getElementById('batchDownloadBtn').addEventListener('click', () => {
            this.batchDownload();
        });

        // 搜索关键词输入事件（实时搜索建议）
        const keywordInput = document.getElementById('keyword');
        let searchTimeout;
        keywordInput.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                this.showSearchSuggestions(e.target.value);
            }, 300);
        });
    }

    async loadInitialData() {
        try {
            // 加载文件类型选项
            const fileTypesResponse = await fetch('/api/search/file-types');
            const fileTypesData = await fileTypesResponse.json();
            
            if (fileTypesData.success) {
                this.populateSelect('fileType', fileTypesData.fileTypes);
            }

            // 加载站点选项
            const sitesResponse = await fetch('/api/search/sites');
            const sitesData = await sitesResponse.json();
            
            if (sitesData.success) {
                this.populateSelect('site', sitesData.sites);
            }
        } catch (error) {
            console.error('加载初始数据失败:', error);
        }
    }

    populateSelect(selectId, options) {
        const select = document.getElementById(selectId);
        select.innerHTML = '';
        
        options.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option.value;
            optionElement.textContent = `${option.icon} ${option.label}`;
            select.appendChild(optionElement);
        });
    }

    async performSearch(page = 1) {
        const keyword = document.getElementById('keyword').value.trim();
        const fileType = document.getElementById('fileType').value;
        const site = document.getElementById('site').value;

        if (!keyword) {
            this.showError('请输入搜索关键词');
            return;
        }

        this.currentQuery = keyword;
        this.currentFileType = fileType;
        this.currentSite = site;
        this.currentPage = page;

        this.showLoading();
        this.hideResults();

        try {
            const response = await fetch('/api/search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    keyword,
                    fileType,
                    site,
                    page
                })
            });

            const data = await response.json();

            if (data.success) {
                this.searchResults = data.data.results;
                this.displayResults(data.data);
            } else {
                this.showError(data.error || '搜索失败，请稍后重试');
            }
        } catch (error) {
            console.error('搜索错误:', error);
            this.showError('网络错误，请检查连接后重试');
        } finally {
            this.hideLoading();
        }
    }

    displayResults(data) {
        const resultsSection = document.getElementById('resultsSection');
        const resultsList = document.getElementById('resultsList');
        const resultsStats = document.getElementById('resultsStats');
        const pagination = document.getElementById('pagination');

        // 显示结果区域
        resultsSection.style.display = 'block';

        // 显示统计信息
        resultsStats.innerHTML = `
            找到约 <span class="stats-highlight">${data.totalResults.toLocaleString()}</span> 个结果
            (用时 <span class="stats-highlight">${data.searchTime}</span> 秒)
        `;

        // 显示结果列表
        if (data.results.length === 0) {
            resultsList.innerHTML = this.getEmptyStateHTML();
        } else {
            resultsList.innerHTML = data.results.map(result => this.getResultItemHTML(result)).join('');
            this.bindResultEvents();
        }

        // 显示分页
        this.displayPagination(data);

        // 重置选择状态
        this.selectedFiles.clear();
        this.updateBatchDownloadButton();
    }

    getResultItemHTML(result) {
        const fileTypeClass = `file-type-${result.fileType.toLowerCase()}`;
        
        return `
            <div class="result-item" data-id="${result.id}">
                <div class="result-checkbox">
                    <input type="checkbox" class="form-check-input" data-url="${result.url}" data-title="${result.title}">
                </div>
                
                <div class="result-actions">
                    <button class="btn btn-download btn-sm" onclick="app.downloadSingle('${result.url}', '${result.title}')">
                        <i class="bi bi-download me-1"></i>下载
                    </button>
                </div>

                <div class="mb-2">
                    <span class="file-type-badge ${fileTypeClass}">${result.fileType}</span>
                    <a href="${result.url}" target="_blank" class="result-title">${result.title}</a>
                </div>

                <div class="result-snippet">${result.snippet}</div>

                <div class="result-meta">
                    <span><i class="bi bi-building me-1"></i>${result.source}</span>
                    <span><i class="bi bi-hdd me-1"></i>${result.fileSize}</span>
                    <span><i class="bi bi-link-45deg me-1"></i>${result.displayUrl}</span>
                </div>
            </div>
        `;
    }

    getEmptyStateHTML() {
        return `
            <div class="empty-state">
                <i class="bi bi-search"></i>
                <h5>没有找到相关文档</h5>
                <p>请尝试：</p>
                <ul class="list-unstyled">
                    <li>• 使用不同的关键词</li>
                    <li>• 选择不同的文件类型</li>
                    <li>• 扩大搜索范围（选择"所有站点"）</li>
                </ul>
            </div>
        `;
    }

    bindResultEvents() {
        // 绑定复选框事件
        document.querySelectorAll('.result-item input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                const resultItem = e.target.closest('.result-item');
                const url = e.target.dataset.url;
                const title = e.target.dataset.title;
                const fileKey = `${url}|${title}`;

                if (e.target.checked) {
                    this.selectedFiles.add(fileKey);
                    resultItem.classList.add('selected');
                } else {
                    this.selectedFiles.delete(fileKey);
                    resultItem.classList.remove('selected');
                }

                this.updateBatchDownloadButton();
            });
        });
    }

    toggleSelectAll() {
        const checkboxes = document.querySelectorAll('.result-item input[type="checkbox"]');
        const allChecked = Array.from(checkboxes).every(cb => cb.checked);

        checkboxes.forEach(checkbox => {
            checkbox.checked = !allChecked;
            const event = new Event('change');
            checkbox.dispatchEvent(event);
        });
    }

    updateBatchDownloadButton() {
        const batchDownloadBtn = document.getElementById('batchDownloadBtn');
        const selectedCount = document.getElementById('selectedCount');
        const count = this.selectedFiles.size;

        selectedCount.textContent = count;
        batchDownloadBtn.disabled = count === 0;
    }

    displayPagination(data) {
        const pagination = document.getElementById('pagination');
        const totalPages = data.totalPages;
        const currentPage = data.currentPage;

        if (totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }

        let paginationHTML = '';

        // 上一页
        if (currentPage > 1) {
            paginationHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="app.performSearch(${currentPage - 1})">
                        <i class="bi bi-chevron-left"></i>
                    </a>
                </li>
            `;
        }

        // 页码
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        if (startPage > 1) {
            paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="app.performSearch(1)">1</a></li>`;
            if (startPage > 2) {
                paginationHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === currentPage ? 'active' : '';
            paginationHTML += `
                <li class="page-item ${activeClass}">
                    <a class="page-link" href="#" onclick="app.performSearch(${i})">${i}</a>
                </li>
            `;
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                paginationHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
            paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="app.performSearch(${totalPages})">${totalPages}</a></li>`;
        }

        // 下一页
        if (currentPage < totalPages) {
            paginationHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="app.performSearch(${currentPage + 1})">
                        <i class="bi bi-chevron-right"></i>
                    </a>
                </li>
            `;
        }

        pagination.innerHTML = paginationHTML;
    }

    async downloadSingle(url, title) {
        try {
            const response = await fetch('/api/download/single', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ url, title })
            });

            if (response.ok) {
                // 创建下载链接
                const blob = await response.blob();
                const downloadUrl = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = downloadUrl;
                a.download = title || 'document';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(downloadUrl);

                this.showSuccess('文件下载成功');
            } else {
                const errorData = await response.json();
                this.showError(errorData.error || '下载失败');
            }
        } catch (error) {
            console.error('下载错误:', error);
            this.showError('下载失败，请稍后重试');
        }
    }

    async batchDownload() {
        if (this.selectedFiles.size === 0) {
            this.showError('请先选择要下载的文件');
            return;
        }

        // 将选中的文件转换为正确的格式
        const files = Array.from(this.selectedFiles).map(fileKey => {
            const [url, title] = fileKey.split('|');
            return { url, title };
        });

        try {
            const response = await fetch('/api/download/batch', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ files })
            });

            const data = await response.json();

            if (data.success) {
                this.showBatchDownloadModal(data.data);
            } else {
                this.showError(data.error || '批量下载验证失败');
            }
        } catch (error) {
            console.error('批量下载错误:', error);
            this.showError('批量下载失败，请稍后重试');
        }
    }

    showBatchDownloadModal(data) {
        const modal = new bootstrap.Modal(document.getElementById('downloadModal'));
        const progressContainer = document.getElementById('downloadProgress');

        let progressHTML = `
            <div class="mb-3">
                <h6>下载统计</h6>
                <p class="mb-1">总文件数: ${data.totalFiles}</p>
                <p class="mb-1">有效文件: ${data.validCount}</p>
                <p class="mb-1">无效文件: ${data.invalidCount}</p>
                <p class="mb-1">总大小: ${data.totalSize}</p>
            </div>
        `;

        if (data.invalidFiles.length > 0) {
            progressHTML += `
                <div class="mb-3">
                    <h6 class="text-danger">无法下载的文件</h6>
                    ${data.invalidFiles.map(file => `
                        <div class="download-item error">
                            <div class="fw-semibold">${file.title}</div>
                            <div class="text-danger small">${file.error}</div>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        if (data.validFiles.length > 0) {
            progressHTML += `
                <div class="mb-3">
                    <h6 class="text-success">准备下载的文件</h6>
                    ${data.validFiles.map(file => `
                        <div class="download-item">
                            <div class="fw-semibold">${file.title}</div>
                            <div class="text-muted small">${file.formattedSize}</div>
                        </div>
                    `).join('')}
                </div>
                <button class="btn btn-success" onclick="app.startBatchDownload(${JSON.stringify(data.validFiles).replace(/"/g, '&quot;')})">
                    <i class="bi bi-download me-1"></i>开始下载
                </button>
            `;
        }

        progressContainer.innerHTML = progressHTML;
        modal.show();
    }

    async startBatchDownload(files) {
        for (const file of files) {
            try {
                await this.downloadSingle(file.url, file.title);
                await new Promise(resolve => setTimeout(resolve, 1000)); // 延迟1秒避免过快请求
            } catch (error) {
                console.error(`下载失败: ${file.title}`, error);
            }
        }
    }

    showLoading() {
        document.getElementById('loadingSection').style.display = 'block';
    }

    hideLoading() {
        document.getElementById('loadingSection').style.display = 'none';
    }

    hideResults() {
        document.getElementById('resultsSection').style.display = 'none';
    }

    showError(message) {
        // 可以使用 Bootstrap 的 Toast 或 Alert 组件
        alert(message); // 简单实现，可以后续优化
    }

    showSuccess(message) {
        // 可以使用 Bootstrap 的 Toast 组件
        console.log(message); // 简单实现，可以后续优化
    }

    async showSearchSuggestions(query) {
        if (query.length < 2) return;

        try {
            const response = await fetch(`/api/search/suggestions?q=${encodeURIComponent(query)}`);
            const data = await response.json();

            if (data.success && data.suggestions.length > 0) {
                // 这里可以实现搜索建议的显示逻辑
                console.log('搜索建议:', data.suggestions);
            }
        } catch (error) {
            console.error('获取搜索建议失败:', error);
        }
    }
}

// 初始化应用
const app = new SosoApp();
