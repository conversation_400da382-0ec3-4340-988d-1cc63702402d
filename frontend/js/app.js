// SOSO 文档搜索工具 - 纯前端版本
class SosoApp {
    constructor() {
        this.currentPage = 1;
        this.currentQuery = '';
        this.currentFileType = 'pdf';
        this.currentSite = 'all';
        this.selectedFiles = new Set();
        this.searchResults = [];

        // 搜索引擎配置
        this.searchEngines = {
            baidu: {
                name: '百度',
                baseUrl: 'https://www.baidu.com/s',
                enabled: true
            },
            bing: {
                name: '必应',
                baseUrl: 'https://www.bing.com/search',
                enabled: true
            }
        };

        this.init();
    }

    init() {
        this.bindEvents();
        this.handleUrlParameters();
        this.initializeDefaults();
    }

    bindEvents() {
        // 搜索表单提交
        document.getElementById('searchForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.performSearch();
        });

        // 全选按钮
        document.getElementById('selectAllBtn').addEventListener('click', () => {
            this.toggleSelectAll();
        });

        // 批量下载按钮
        document.getElementById('batchDownloadBtn').addEventListener('click', () => {
            this.batchDownload();
        });
    }

    initializeDefaults() {
        // 设置默认文件类型为PDF
        const pdfRadio = document.getElementById('fileTypePdf');
        if (pdfRadio && !document.querySelector('input[name="fileType"]:checked')) {
            pdfRadio.checked = true;
        }

        // 初始化站点选择
        this.populateSiteOptions();
    }

    populateSiteOptions() {
        const siteSelect = document.getElementById('site');
        const sites = [
            { value: 'all', label: '🌐 所有站点' },
            { value: 'wechat', label: '💬 微信公众号' },
            { value: 'zhihu', label: '🤔 知乎' },
            { value: 'zsxq', label: '⭐ 知识星球' },
            { value: 'csdn', label: '💻 CSDN' },
            { value: 'jianshu', label: '✍️ 简书' },
            { value: 'gov', label: '🏛️ 政府网站' },
            { value: 'edu', label: '🎓 教育网站' }
        ];

        siteSelect.innerHTML = '';
        sites.forEach(site => {
            const option = document.createElement('option');
            option.value = site.value;
            option.textContent = site.label;
            siteSelect.appendChild(option);
        });
    }

    /**
     * 构建搜索查询字符串
     */
    buildSearchQuery(keyword, fileType, site) {
        let query = keyword;

        // 添加文件类型限制
        if (fileType && fileType !== 'all') {
            const fileTypeMap = {
                'pdf': 'pdf',
                'word': 'doc OR docx',
                'ppt': 'ppt OR pptx',
                'excel': 'xls OR xlsx'
            };

            if (fileTypeMap[fileType]) {
                query += ` filetype:${fileTypeMap[fileType]}`;
            }
        }

        // 添加站点限制
        if (site && site !== 'all') {
            const siteMap = {
                'wechat': 'mp.weixin.qq.com',
                'zhihu': 'zhihu.com',
                'zsxq': 'zsxq.com',
                'csdn': 'csdn.net',
                'jianshu': 'jianshu.com',
                'gov': 'gov.cn',
                'edu': 'edu.cn'
            };

            if (siteMap[site]) {
                query += ` site:${siteMap[site]}`;
            }
        }

        return query;
    }

    /**
     * 处理URL参数
     */
    handleUrlParameters() {
        try {
            const urlParams = new URLSearchParams(window.location.search);

            // 处理文件类型参数
            const fileType = urlParams.get('fileType');
            if (fileType) {
                const fileTypeRadio = document.querySelector(`input[name="fileType"][value="${fileType}"]`);
                if (fileTypeRadio) {
                    // 先取消所有选中状态
                    document.querySelectorAll('input[name="fileType"]').forEach(radio => {
                        radio.checked = false;
                    });
                    // 选中指定的文件类型
                    fileTypeRadio.checked = true;
                }
            }

            // 处理搜索关键词参数
            const keyword = urlParams.get('keyword') || urlParams.get('q');
            if (keyword) {
                document.getElementById('keyword').value = decodeURIComponent(keyword);
            }

            // 处理站点参数
            const site = urlParams.get('site');
            if (site) {
                const siteSelect = document.getElementById('site');
                if (siteSelect) {
                    siteSelect.value = site;
                }
            }

            // 如果有关键词，自动执行搜索
            if (keyword) {
                // 延迟执行，确保页面完全加载
                setTimeout(() => {
                    this.performSearch();
                }, 500);
            }

        } catch (error) {
            console.error('处理URL参数失败:', error);
        }
    }

    /**
     * 更新URL参数
     */
    updateUrlParameters(keyword, fileType, site, page) {
        try {
            const url = new URL(window.location);

            // 更新参数
            if (keyword) {
                url.searchParams.set('keyword', keyword);
            } else {
                url.searchParams.delete('keyword');
            }

            if (fileType && fileType !== 'all') {
                url.searchParams.set('fileType', fileType);
            } else {
                url.searchParams.delete('fileType');
            }

            if (site && site !== 'all') {
                url.searchParams.set('site', site);
            } else {
                url.searchParams.delete('site');
            }

            if (page && page > 1) {
                url.searchParams.set('page', page);
            } else {
                url.searchParams.delete('page');
            }

            // 更新浏览器URL，但不刷新页面
            window.history.replaceState({}, '', url);

        } catch (error) {
            console.error('更新URL参数失败:', error);
        }
    }



    async performSearch(page = 1) {
        const keyword = document.getElementById('keyword').value.trim();
        const fileTypeRadio = document.querySelector('input[name="fileType"]:checked');
        const fileType = fileTypeRadio ? fileTypeRadio.value : 'pdf';
        const site = document.getElementById('site').value;

        if (!keyword) {
            this.showError('请输入搜索关键词');
            return;
        }

        this.currentQuery = keyword;
        this.currentFileType = fileType;
        this.currentSite = site;
        this.currentPage = page;

        // 更新URL参数
        this.updateUrlParameters(keyword, fileType, site, page);

        this.showLoading();
        this.hideResults();

        try {
            // 生成模拟搜索结果
            const searchResults = this.generateSearchResults(keyword, fileType, site, page);
            this.searchResults = searchResults.results;
            this.displayResults(searchResults);
        } catch (error) {
            console.error('搜索错误:', error);
            this.showError('搜索失败，请稍后重试');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * 生成模拟搜索结果
     */
    generateSearchResults(keyword, fileType, site, page = 1) {
        const startIndex = (page - 1) * 10 + 1;

        // 基础结果模板
        const baseResults = [
            {
                title: `${keyword} - 技术规范文档`,
                url: `https://standards.gov.cn/documents/${encodeURIComponent(keyword)}.pdf`,
                snippet: `这是关于${keyword}的国家技术规范文档，包含了详细的技术标准和实施要求。本文档由相关部门制定，具有权威性和指导性。`,
                fileType: 'PDF',
                source: '国家标准化管理委员会',
                fileSize: '3.2 MB'
            },
            {
                title: `${keyword} - 研究报告`,
                url: `https://research.edu.cn/reports/${encodeURIComponent(keyword)}_report.pdf`,
                snippet: `${keyword}领域的最新研究报告，详细分析了当前发展状况、技术趋势和未来展望。报告基于大量实证数据和案例分析。`,
                fileType: 'PDF',
                source: '中国科学院',
                fileSize: '8.7 MB'
            },
            {
                title: `${keyword} - 实施指南`,
                url: `https://docs.industry.com/guides/${encodeURIComponent(keyword)}_guide.docx`,
                snippet: `${keyword}的详细实施指南，包含步骤说明、最佳实践和常见问题解答。适用于企业和组织的实际应用。`,
                fileType: 'Word',
                source: '行业协会',
                fileSize: '2.1 MB'
            },
            {
                title: `${keyword} - 培训课件`,
                url: `https://training.edu.cn/courses/${encodeURIComponent(keyword)}_training.pptx`,
                snippet: `${keyword}专业培训课件，包含理论知识、实践案例和操作演示。适合教学和企业培训使用。`,
                fileType: 'PPT',
                source: '教育培训机构',
                fileSize: '15.3 MB'
            },
            {
                title: `${keyword} - 数据统计表`,
                url: `https://stats.gov.cn/data/${encodeURIComponent(keyword)}_statistics.xlsx`,
                snippet: `${keyword}相关的统计数据表格，包含历年数据、趋势分析和预测模型。数据来源权威可靠。`,
                fileType: 'Excel',
                source: '国家统计局',
                fileSize: '4.5 MB'
            },
            {
                title: `${keyword} - 白皮书`,
                url: `https://whitepaper.org.cn/papers/${encodeURIComponent(keyword)}_whitepaper.pdf`,
                snippet: `${keyword}行业白皮书，深入分析行业现状、发展机遇和挑战。为政策制定和企业决策提供参考。`,
                fileType: 'PDF',
                source: '行业研究机构',
                fileSize: '6.8 MB'
            },
            {
                title: `${keyword} - 案例分析`,
                url: `https://cases.business.com/studies/${encodeURIComponent(keyword)}_case.pdf`,
                snippet: `${keyword}的典型案例分析，包含成功案例和失败教训。通过实际案例展示最佳实践和注意事项。`,
                fileType: 'PDF',
                source: '商业研究中心',
                fileSize: '5.2 MB'
            },
            {
                title: `${keyword} - 操作手册`,
                url: `https://manual.tech.com/handbooks/${encodeURIComponent(keyword)}_manual.docx`,
                snippet: `${keyword}详细操作手册，包含步骤说明、技术参数和故障排除。适合技术人员和操作员使用。`,
                fileType: 'Word',
                source: '技术服务公司',
                fileSize: '3.7 MB'
            }
        ];

        // 根据文件类型过滤
        let filteredResults = baseResults;
        if (fileType !== 'all') {
            const typeMap = {
                'pdf': 'PDF',
                'word': 'Word',
                'ppt': 'PPT',
                'excel': 'Excel'
            };
            filteredResults = baseResults.filter(result => result.fileType === typeMap[fileType]);
        }

        // 转换为完整的结果格式
        const results = filteredResults.slice(0, 10).map((result, index) => ({
            id: `result_${startIndex + index}`,
            title: result.title,
            url: result.url,
            snippet: result.snippet,
            fileType: result.fileType,
            source: result.source,
            fileSize: result.fileSize,
            displayUrl: new URL(result.url).hostname,
            formattedUrl: result.url
        }));

        return {
            results,
            totalResults: 1000,
            searchTime: '0.3',
            query: keyword,
            processedQuery: this.buildSearchQuery(keyword, fileType, site),
            currentPage: page,
            totalPages: Math.ceil(1000 / 10),
            hasNextPage: page < 100
        };
    }

    displayResults(data) {
        const resultsSection = document.getElementById('resultsSection');
        const resultsList = document.getElementById('resultsList');
        const resultsStats = document.getElementById('resultsStats');
        const pagination = document.getElementById('pagination');

        // 显示结果区域
        resultsSection.style.display = 'block';

        // 显示统计信息
        resultsStats.innerHTML = `
            找到约 <span class="stats-highlight">${data.totalResults.toLocaleString()}</span> 个结果
            (用时 <span class="stats-highlight">${data.searchTime}</span> 秒)
        `;

        // 显示结果列表
        if (data.results.length === 0) {
            resultsList.innerHTML = this.getEmptyStateHTML();
        } else {
            resultsList.innerHTML = data.results.map(result => this.getResultItemHTML(result)).join('');
            this.bindResultEvents();
        }

        // 显示分页
        this.displayPagination(data);

        // 重置选择状态
        this.selectedFiles.clear();
        this.updateBatchDownloadButton();
    }

    getResultItemHTML(result) {
        const fileTypeClass = `file-type-${result.fileType.toLowerCase()}`;
        
        return `
            <div class="result-item" data-id="${result.id}">
                <div class="result-checkbox">
                    <input type="checkbox" class="form-check-input" data-url="${result.url}" data-title="${result.title}">
                </div>
                
                <div class="result-actions">
                    <button class="btn btn-download btn-sm" onclick="app.downloadSingle('${result.url}', '${result.title}')">
                        <i class="bi bi-download me-1"></i>下载
                    </button>
                </div>

                <div class="mb-2">
                    <span class="file-type-badge ${fileTypeClass}">${result.fileType}</span>
                    <a href="${result.url}" target="_blank" class="result-title">${result.title}</a>
                </div>

                <div class="result-snippet">${result.snippet}</div>

                <div class="result-meta">
                    <span><i class="bi bi-building me-1"></i>${result.source}</span>
                    <span><i class="bi bi-hdd me-1"></i>${result.fileSize}</span>
                    <span><i class="bi bi-link-45deg me-1"></i>${result.displayUrl}</span>
                </div>
            </div>
        `;
    }

    getEmptyStateHTML() {
        return `
            <div class="empty-state">
                <i class="bi bi-search"></i>
                <h5>没有找到相关文档</h5>
                <p>请尝试：</p>
                <ul class="list-unstyled">
                    <li>• 使用不同的关键词</li>
                    <li>• 选择不同的文件类型</li>
                    <li>• 扩大搜索范围（选择"所有站点"）</li>
                </ul>
            </div>
        `;
    }

    bindResultEvents() {
        // 绑定复选框事件
        document.querySelectorAll('.result-item input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                const resultItem = e.target.closest('.result-item');
                const url = e.target.dataset.url;
                const title = e.target.dataset.title;
                const fileKey = `${url}|${title}`;

                if (e.target.checked) {
                    this.selectedFiles.add(fileKey);
                    resultItem.classList.add('selected');
                } else {
                    this.selectedFiles.delete(fileKey);
                    resultItem.classList.remove('selected');
                }

                this.updateBatchDownloadButton();
            });
        });
    }

    toggleSelectAll() {
        const checkboxes = document.querySelectorAll('.result-item input[type="checkbox"]');
        const allChecked = Array.from(checkboxes).every(cb => cb.checked);

        checkboxes.forEach(checkbox => {
            checkbox.checked = !allChecked;
            const event = new Event('change');
            checkbox.dispatchEvent(event);
        });
    }

    updateBatchDownloadButton() {
        const batchDownloadBtn = document.getElementById('batchDownloadBtn');
        const selectedCount = document.getElementById('selectedCount');
        const count = this.selectedFiles.size;

        selectedCount.textContent = count;
        batchDownloadBtn.disabled = count === 0;
    }

    displayPagination(data) {
        const pagination = document.getElementById('pagination');
        const totalPages = data.totalPages;
        const currentPage = data.currentPage;

        if (totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }

        let paginationHTML = '';

        // 上一页
        if (currentPage > 1) {
            paginationHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="app.performSearch(${currentPage - 1})">
                        <i class="bi bi-chevron-left"></i>
                    </a>
                </li>
            `;
        }

        // 页码
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        if (startPage > 1) {
            paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="app.performSearch(1)">1</a></li>`;
            if (startPage > 2) {
                paginationHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === currentPage ? 'active' : '';
            paginationHTML += `
                <li class="page-item ${activeClass}">
                    <a class="page-link" href="#" onclick="app.performSearch(${i})">${i}</a>
                </li>
            `;
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                paginationHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
            paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="app.performSearch(${totalPages})">${totalPages}</a></li>`;
        }

        // 下一页
        if (currentPage < totalPages) {
            paginationHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="app.performSearch(${currentPage + 1})">
                        <i class="bi bi-chevron-right"></i>
                    </a>
                </li>
            `;
        }

        pagination.innerHTML = paginationHTML;
    }

    downloadSingle(url, title) {
        try {
            // 直接在新窗口中打开下载链接
            const link = document.createElement('a');
            link.href = url;
            link.target = '_blank';
            link.download = title || 'document';

            // 添加到页面并点击
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            this.showSuccess(`开始下载: ${title}`);
        } catch (error) {
            console.error('下载错误:', error);
            this.showError('下载失败，请稍后重试');
        }
    }

    async batchDownload() {
        if (this.selectedFiles.size === 0) {
            this.showError('请先选择要下载的文件');
            return;
        }

        // 将选中的文件转换为正确的格式
        const files = Array.from(this.selectedFiles).map(fileKey => {
            const [url, title] = fileKey.split('|');
            return { url, title };
        });

        this.showBatchDownloadModal(files);
    }

    showBatchDownloadModal(files) {
        const modal = new bootstrap.Modal(document.getElementById('downloadModal'));
        const progressContainer = document.getElementById('downloadProgress');

        let progressHTML = `
            <div class="mb-3">
                <h6>批量下载</h6>
                <p class="mb-1">准备下载 ${files.length} 个文件</p>
            </div>
            <div class="mb-3">
                <h6 class="text-success">文件列表</h6>
                ${files.map((file, index) => `
                    <div class="download-item" id="download-item-${index}">
                        <div class="fw-semibold">${file.title}</div>
                        <div class="text-muted small">准备下载...</div>
                    </div>
                `).join('')}
            </div>
            <button class="btn btn-success" onclick="app.startBatchDownload(${JSON.stringify(files).replace(/"/g, '&quot;')})">
                <i class="bi bi-download me-1"></i>开始下载
            </button>
        `;

        progressContainer.innerHTML = progressHTML;
        modal.show();
    }

    async startBatchDownload(files) {
        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            const itemElement = document.getElementById(`download-item-${i}`);

            try {
                if (itemElement) {
                    itemElement.querySelector('.text-muted').textContent = '正在下载...';
                    itemElement.classList.add('downloading');
                }

                this.downloadSingle(file.url, file.title);

                if (itemElement) {
                    itemElement.querySelector('.text-muted').textContent = '下载完成';
                    itemElement.classList.remove('downloading');
                    itemElement.classList.add('success');
                }

                // 延迟1秒避免过快请求
                await new Promise(resolve => setTimeout(resolve, 1000));
            } catch (error) {
                console.error(`下载失败: ${file.title}`, error);
                if (itemElement) {
                    itemElement.querySelector('.text-muted').textContent = '下载失败';
                    itemElement.classList.remove('downloading');
                    itemElement.classList.add('error');
                }
            }
        }

        this.showSuccess(`批量下载完成，共 ${files.length} 个文件`);
    }

    showLoading() {
        document.getElementById('loadingSection').style.display = 'block';
    }

    hideLoading() {
        document.getElementById('loadingSection').style.display = 'none';
    }

    hideResults() {
        document.getElementById('resultsSection').style.display = 'none';
    }

    showError(message) {
        // 创建错误提示
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        alertDiv.innerHTML = `
            <i class="bi bi-exclamation-triangle-fill me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(alertDiv);

        // 3秒后自动移除
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 3000);
    }

    showSuccess(message) {
        // 创建成功提示
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        alertDiv.innerHTML = `
            <i class="bi bi-check-circle-fill me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(alertDiv);

        // 3秒后自动移除
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 3000);
    }
}

// 初始化应用
const app = new SosoApp();
