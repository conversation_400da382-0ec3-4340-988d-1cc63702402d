/* 全局样式 */
body {
    font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    line-height: 1.6;
}

/* 导航栏样式 */
.navbar-brand {
    font-size: 1.5rem;
    letter-spacing: 1px;
}

/* 搜索表单样式 */
.card {
    border: none;
    border-radius: 12px;
}

.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.btn-primary {
    background: linear-gradient(45deg, #0d6efd, #6610f2);
    border: none;
    border-radius: 8px;
    font-weight: 500;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #0b5ed7, #5a0fc8);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 搜索结果样式 */
.result-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    position: relative;
}

.result-item:hover {
    border-color: #0d6efd;
    box-shadow: 0 4px 12px rgba(13, 110, 253, 0.1);
    transform: translateY(-2px);
}

.result-item.selected {
    border-color: #0d6efd;
    background-color: #f8f9ff;
}

.file-type-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    border-radius: 6px;
    margin-right: 0.5rem;
}

.file-type-pdf {
    background-color: #dc3545;
    color: white;
}

.file-type-word {
    background-color: #2b579a;
    color: white;
}

.file-type-ppt {
    background-color: #d04423;
    color: white;
}

.file-type-excel {
    background-color: #217346;
    color: white;
}

.file-type-article {
    background-color: #6c757d;
    color: white;
}

.result-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #212529;
    text-decoration: none;
    line-height: 1.4;
}

.result-title:hover {
    color: #0d6efd;
    text-decoration: underline;
}

.result-snippet {
    color: #6c757d;
    font-size: 0.9rem;
    margin: 0.5rem 0;
    line-height: 1.5;
}

.result-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    font-size: 0.85rem;
    color: #6c757d;
    margin-top: 0.75rem;
}

.result-meta span {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.result-actions {
    position: absolute;
    top: 1rem;
    right: 1rem;
    display: flex;
    gap: 0.5rem;
}

.result-checkbox {
    position: absolute;
    top: 1rem;
    left: 1rem;
}

.result-checkbox input[type="checkbox"] {
    transform: scale(1.2);
}

/* 下载按钮样式 */
.btn-download {
    background: linear-gradient(45deg, #28a745, #20c997);
    border: none;
    color: white;
    font-weight: 500;
    border-radius: 6px;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

.btn-download:hover {
    background: linear-gradient(45deg, #218838, #1ea085);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-download:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 分页样式 */
.pagination .page-link {
    border-radius: 6px;
    margin: 0 2px;
    border: 1px solid #dee2e6;
    color: #0d6efd;
}

.pagination .page-link:hover {
    background-color: #e9ecef;
    border-color: #0d6efd;
}

.pagination .page-item.active .page-link {
    background: linear-gradient(45deg, #0d6efd, #6610f2);
    border-color: #0d6efd;
}

/* 加载动画 */
.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* 统计信息样式 */
.stats-highlight {
    color: #0d6efd;
    font-weight: 600;
}

/* 下载进度样式 */
.download-item {
    padding: 0.75rem;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    margin-bottom: 0.5rem;
    background: white;
}

.download-item.success {
    border-color: #28a745;
    background-color: #f8fff9;
}

.download-item.error {
    border-color: #dc3545;
    background-color: #fff8f8;
}

.download-item.downloading {
    border-color: #0d6efd;
    background-color: #f8f9ff;
}

.progress {
    height: 6px;
    border-radius: 3px;
}

/* 文件类型选项样式 */
.file-type-options {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
}

.file-type-options .form-check {
    margin-bottom: 0.5rem;
}

.file-type-options .form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.file-type-options .form-check-label {
    font-size: 0.9rem;
    cursor: pointer;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    transition: background-color 0.2s ease;
}

.file-type-options .form-check-input:checked + .form-check-label {
    background-color: rgba(13, 110, 253, 0.1);
    color: #0d6efd;
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .file-type-options {
        flex-direction: column;
        gap: 0.5rem;
    }
    .result-actions {
        position: static;
        margin-top: 1rem;
        justify-content: flex-end;
    }
    
    .result-checkbox {
        position: static;
        margin-bottom: 0.5rem;
    }
    
    .result-meta {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.result-item {
    animation: fadeIn 0.5s ease-out;
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

/* 徽章样式 */
.badge {
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* 错误状态样式 */
.error-state {
    text-align: center;
    padding: 2rem 1rem;
    color: #dc3545;
    background-color: #fff5f5;
    border: 1px solid #fed7d7;
    border-radius: 8px;
    margin: 1rem 0;
}

.error-state i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}
