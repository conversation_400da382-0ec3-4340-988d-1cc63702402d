<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SOSO - 文档搜索下载工具</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- 自定义样式 -->
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand fw-bold" href="#">
                <i class="bi bi-search me-2"></i>SOSO
            </a>
            <span class="navbar-text text-light">
                高效易用的文档搜索下载工具
            </span>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container mt-4">
        <!-- 搜索区域 -->
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card shadow-sm">
                    <div class="card-body p-4">
                        <form id="searchForm">
                            <!-- 搜索关键词 -->
                            <div class="mb-3">
                                <label for="keyword" class="form-label fw-semibold">
                                    <i class="bi bi-search me-1"></i>搜索关键词
                                </label>
                                <div class="input-group">
                                    <input type="text" 
                                           class="form-control form-control-lg" 
                                           id="keyword" 
                                           placeholder="请输入要搜索的文档关键词..."
                                           required>
                                    <button class="btn btn-primary btn-lg" type="submit" id="searchBtn">
                                        <i class="bi bi-search me-1"></i>搜索
                                    </button>
                                </div>
                                <div class="form-text">
                                    支持搜索PDF、Word、PPT等文档，以及普通文章内容
                                </div>
                            </div>

                            <!-- 搜索选项 -->
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-semibold">
                                        <i class="bi bi-file-earmark me-1"></i>文件类型
                                    </label>
                                    <div class="file-type-options">
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="fileType" id="fileTypeAll" value="all">
                                            <label class="form-check-label" for="fileTypeAll">📄 所有类型</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="fileType" id="fileTypePdf" value="pdf" checked>
                                            <label class="form-check-label" for="fileTypePdf">📕 PDF文档</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="fileType" id="fileTypeWord" value="word">
                                            <label class="form-check-label" for="fileTypeWord">📘 Word文档</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="fileType" id="fileTypePpt" value="ppt">
                                            <label class="form-check-label" for="fileTypePpt">📙 PPT演示</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="fileType" id="fileTypeExcel" value="excel">
                                            <label class="form-check-label" for="fileTypeExcel">📗 Excel表格</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="site" class="form-label fw-semibold">
                                        <i class="bi bi-globe me-1"></i>搜索站点
                                    </label>
                                    <select class="form-select" id="site">
                                        <option value="all">🌐 所有站点</option>
                                        <option value="wechat">💬 微信公众号</option>
                                        <option value="zhihu">🤔 知乎</option>
                                        <option value="zsxq">⭐ 知识星球</option>
                                        <option value="csdn">💻 CSDN</option>
                                        <option value="jianshu">✍️ 简书</option>
                                        <option value="gov">🏛️ 政府网站</option>
                                        <option value="edu">🎓 教育网站</option>
                                    </select>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索结果区域 -->
        <div class="row justify-content-center mt-4" id="resultsSection" style="display: none;">
            <div class="col-lg-10">
                <!-- 结果统计 -->
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div id="resultsStats" class="text-muted"></div>
                    <div>
                        <button class="btn btn-outline-primary btn-sm me-2" id="selectAllBtn">
                            <i class="bi bi-check-all me-1"></i>全选
                        </button>
                        <button class="btn btn-success btn-sm" id="batchDownloadBtn" disabled>
                            <i class="bi bi-download me-1"></i>批量下载 (<span id="selectedCount">0</span>)
                        </button>
                    </div>
                </div>

                <!-- 结果列表 -->
                <div id="resultsList"></div>

                <!-- 分页 -->
                <nav aria-label="搜索结果分页" class="mt-4">
                    <ul class="pagination justify-content-center" id="pagination"></ul>
                </nav>
            </div>
        </div>

        <!-- 加载状态 -->
        <div class="row justify-content-center mt-4" id="loadingSection" style="display: none;">
            <div class="col-lg-6 text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">搜索中...</span>
                </div>
                <p class="mt-2 text-muted">正在搜索文档，请稍候...</p>
            </div>
        </div>
    </div>

    <!-- 下载进度模态框 -->
    <div class="modal fade" id="downloadModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-download me-2"></i>下载进度
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="downloadProgress"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-light mt-5 py-4">
        <div class="container text-center">
            <p class="text-muted mb-0">
                <i class="bi bi-heart-fill text-danger me-1"></i>
                SOSO 文档搜索工具 - 让文档搜索更简单
            </p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 自定义脚本 -->
    <script src="js/app.js"></script>
</body>
</html>
